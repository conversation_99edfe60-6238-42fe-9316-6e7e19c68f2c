.filterBar {
  list-style-type: none;
  background-color: #ddd;
  padding: 6px 6px 0;
}

.searchInput {
  background: #fff;
  height: 30px;
  display: block;
  width: 100%;
  padding: 8px 16px;
  font-size: 13px;
  line-height: 1.5384616;
  color: #333333;
  border-radius: 3px;
}

@media (max-width: 768px) {
  .searchButton > a {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
  }

  .filterBarItem {
    display: block;
    float: none;

    > a {
      padding-top: 10px !important;
      padding-bottom: 10px !important;
    }

    :global(.bootstrap-select) {
      height: auto !important;

      :global(.btn) {
        padding-left: 19px !important;
        margin-top: -1px !important;
      }
      :global(.caret){
        right: 8px !important;
      }
    }
  }
}

@media (min-width: 769px) {
  .filterBarItem {
    max-width: calc(100% - 14px);
  }
}

.filterBarItem {
  [class*='spinner-wrap'] {
    padding: 4px 6px;
  }
}
.filterBarItem:empty {
  display: none;
}
.advancedFilterButton {
  background-color: transparent !important;
  border: none !important;
  margin-top: 13px !important;
  @media (max-width: 768px) {
    background: #fff;
    float: none !important;
    margin-top: 8px !important;
    & > div {
      float: none !important;

      a {
        text-align: center;
        width: 100%;
        background: #fff;
        padding: 12px 20px;
        border-radius: 3px;
        border: 1px solid #dddddd;
        display: block;
      }
    }
  }
}

.advancedFilter {
  position: absolute;
  border-top: 0;
  width: 100%;
  left: 0;
  margin-top: -20px;
  margin-bottom: 0;
}

.hidden {
  display: none;
}

.navbarAdjustment{
  overflow: visible !important;

  :global(.nav.navbar-nav){
    overflow: visible !important;
  }

  :global(div.dropdown-menu) {
    position: absolute !important;
    overflow: visible !important;
    top: 100% !important;
    left: 0 !important;
    height: 100% !important;
    min-height: max-content !important;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23), 0 -1px 1px rgba(0, 0, 0, 0.1) !important;
  }

  // Override specific component menu classes to ensure min-height: max-content takes precedence
  :global(div.dropdown-menu.ContentSortingSelector-menu) {
    min-height: max-content !important;
  }
  
  :global(div.dropdown-menu > div.dropdown-menu) {
    position: relative !important;
    overflow: hidden !important;
    top: 0% !important;
    height: 100% !important;
    box-shadow: none !important;
  }
  
  :global(div.dropdown-menu > ul) {
    position: relative !important;
    top: 0 !important;
    margin: 0;
    box-shadow: none !important;
  }
}